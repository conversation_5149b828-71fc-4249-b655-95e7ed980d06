package com.ffnex.youxuan.mall.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ffnex.youxuan.common.core.util.R;
import com.ffnex.youxuan.common.log.annotation.SysLog;
import com.ffnex.youxuan.common.excel.annotation.ResponseExcel;
import com.ffnex.youxuan.common.excel.annotation.RequestExcel;
import com.ffnex.youxuan.mall.entity.UserProfileEntity;
import com.ffnex.youxuan.mall.service.UserProfileService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.ffnex.youxuan.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 用户档案
 *
 * <AUTHOR>
 * @date 2025-08-19 21:45:27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/userProfile" )
@Tag(description = "userProfile" , name = "用户档案管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class UserProfileController {

    private final  UserProfileService userProfileService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param userProfile 用户档案
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("mall_userProfile_view")
    public R getUserProfilePage(@ParameterObject Page page, @ParameterObject UserProfileEntity userProfile) {
        LambdaQueryWrapper<UserProfileEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(Objects.nonNull(userProfile.getPartnerId()),UserProfileEntity::getPartnerId,userProfile.getPartnerId());
		wrapper.eq(Objects.nonNull(userProfile.getCustomerLevel()),UserProfileEntity::getCustomerLevel,userProfile.getCustomerLevel());
		wrapper.eq(StrUtil.isNotBlank(userProfile.getName()),UserProfileEntity::getName,userProfile.getName());
		wrapper.eq(StrUtil.isNotBlank(userProfile.getPhone()),UserProfileEntity::getPhone,userProfile.getPhone());
        return R.ok(userProfileService.page(page, wrapper));
    }


    /**
     * 通过条件查询用户档案
     * @param userProfile 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("mall_userProfile_view")
    public R getDetails(@ParameterObject UserProfileEntity userProfile) {
        return R.ok(userProfileService.list(Wrappers.query(userProfile)));
    }

    /**
     * 新增用户档案
     * @param userProfile 用户档案
     * @return R
     */
    @Operation(summary = "新增用户档案" , description = "新增用户档案" )
    @SysLog("新增用户档案" )
    @PostMapping
    @HasPermission("mall_userProfile_add")
    public R save(@RequestBody UserProfileEntity userProfile) {
        return R.ok(userProfileService.save(userProfile));
    }

    /**
     * 修改用户档案
     * @param userProfile 用户档案
     * @return R
     */
    @Operation(summary = "修改用户档案" , description = "修改用户档案" )
    @SysLog("修改用户档案" )
    @PutMapping
    @HasPermission("mall_userProfile_edit")
    public R updateById(@RequestBody UserProfileEntity userProfile) {
        return R.ok(userProfileService.updateById(userProfile));
    }

    /**
     * 通过id删除用户档案
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除用户档案" , description = "通过id删除用户档案" )
    @SysLog("通过id删除用户档案" )
    @DeleteMapping
    @HasPermission("mall_userProfile_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(userProfileService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param userProfile 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("mall_userProfile_export")
    public List<UserProfileEntity> exportExcel(UserProfileEntity userProfile,Long[] ids) {
        return userProfileService.list(Wrappers.lambdaQuery(userProfile).in(ArrayUtil.isNotEmpty(ids), UserProfileEntity::getId, ids));
    }

    /**
     * 导入excel 表
     * @param userProfileList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("mall_userProfile_export")
    public R importExcel(@RequestExcel List<UserProfileEntity> userProfileList, BindingResult bindingResult) {
        return R.ok(userProfileService.saveBatch(userProfileList));
    }
}
